[{"id": 1, "groudLevelId": 1, "storyId": 1, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [0, 100], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [100, 0], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [0, 100, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 2, "groudLevelId": 1, "storyId": 2, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [0, 100], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [100, 0], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [0, 100, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 3, "groudLevelId": 1, "storyId": 3, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [0, 100], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [100, 0], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [0, 100, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 4, "groudLevelId": 1, "storyId": 4, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [0, 100], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [100, 0], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [0, 100, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 5, "groudLevelId": 1, "storyId": 5, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [0, 100], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [100, 0], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [0, 100, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 6, "groudLevelId": 2, "storyId": 1, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 7, "groudLevelId": 2, "storyId": 2, "levelTargetRatio": [40, 30, 30], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 8, "groudLevelId": 2, "storyId": 3, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 9, "groudLevelId": 2, "storyId": 4, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 10, "groudLevelId": 2, "storyId": 5, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 11, "groudLevelId": 3, "storyId": 1, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 12, "groudLevelId": 3, "storyId": 2, "levelTargetRatio": [40, 30, 30], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 13, "groudLevelId": 3, "storyId": 3, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 14, "groudLevelId": 3, "storyId": 4, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 15, "groudLevelId": 3, "storyId": 5, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 16, "groudLevelId": 4, "storyId": 1, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 17, "groudLevelId": 4, "storyId": 2, "levelTargetRatio": [40, 30, 30], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 18, "groudLevelId": 4, "storyId": 3, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 19, "groudLevelId": 4, "storyId": 4, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 20, "groudLevelId": 4, "storyId": 5, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 21, "groudLevelId": 5, "storyId": 1, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 22, "groudLevelId": 5, "storyId": 2, "levelTargetRatio": [40, 30, 30], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 23, "groudLevelId": 5, "storyId": 3, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 24, "groudLevelId": 5, "storyId": 4, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"id": 25, "groudLevelId": 5, "storyId": 5, "levelTargetRatio": [40, 40, 20], "easyExperienceType": [1, 2], "easyExperienceWeight": [5, 95], "complexityUpWeight1": [95, 5], "complexityDownWeight1": [95, 5], "normalExperienceType": [1, 2, 3], "normalExperienceWeight": [10, 90, 0], "complexityUpWeight2": [90, 10], "complexityDownWeight2": [90, 10], "hardExperienceType": [1, 2, 3], "hardExperienceWeight": [10, 90, 0], "complexityUpWeight3": [90, 10], "complexityDownWeight3": [90, 10], "levelProgressDiffTrigger": 1, "challengeLimitedTime": 0}, {"levelTargetRatio": [1]}]