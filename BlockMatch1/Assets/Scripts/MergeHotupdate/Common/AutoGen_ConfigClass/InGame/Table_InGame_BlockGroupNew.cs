/************************************************
 * Config class : Table_InGame_BlockGroupNew
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.InGame
{
    public partial class Table_InGame_BlockGroupNew:ConfigBase
    {   
        /// <summary>
        /// #ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// 出块列表
        /// </summary>
        public List<int> Block_ids { get; set; }
        
        /// <summary>
        /// 排序权重
        /// </summary>
        public List<int> Weights { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}