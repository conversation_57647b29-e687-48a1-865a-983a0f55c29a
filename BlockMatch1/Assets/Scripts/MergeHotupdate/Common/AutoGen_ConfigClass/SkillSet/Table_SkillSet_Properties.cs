/************************************************
 * Config class : Table_SkillSet_Properties
 * This file is can not be modify !!!
 ************************************************/

using System;
using System.Collections.Generic;
using Config;

namespace DragonPlus.Config.SkillSet
{
    public partial class Table_SkillSet_Properties:ConfigBase
    {   
        /// <summary>
        /// #ID
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// #字段名
        /// </summary>
        public string ParamName { get; set; }
        
        /// <summary>
        /// #剧本触发条件(多剧本同时可触发时，优先匹配次数少的剧本（增加匹配次数少的权重）)
        /// </summary>
        public string Des { get; set; }
        

        public override int GetId()
        {
            return Id;
        }
    }
}