// **********************************************
// Copyright(c) 2021 by com.ustar
// All right reserved
// 
// Author : <PERSON><PERSON><PERSON>
// Date : 2023/07/03/12:07
// Ver : 1.0.0
// Description : Events.cs
// ChangeLog :
// **********************************************

using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using DragonPlus.Config.Global;
using DragonPlus.Core;
using UnityEngine;
using Object = System.Object;

namespace TMGame
{
    
    public struct EventSingIn : IEvent
    {
        
    }
    
    public struct EnterBlockGameEvent : IEvent
    {
        public EnumBlockGameType gameType;
        public EnterBlockGameEvent(EnumBlockGameType Type)
        {
            gameType = Type;
        }
    }

    public struct ExitBlockPlayEvent : IEvent
    {
        public int Type;

        public ExitBlockPlayEvent(int Type)
        {
            this.Type = Type;
        }
    }

    public struct ExitGameEvent : IEvent
    {
        public EnumGameResultType resType;

        public ExitGameEvent(EnumGameResultType Type)
        {
            resType = Type;
        }
    }

    public struct ReplayGameEvent : IEvent
    {
    }

    public struct BlockGameOver : IEvent
    {
        public int curLife;

        public BlockGameOver(int life)
        {
            curLife = life;
        }
    }

    public struct EventGameGemAchive : IEvent
    {
        public List<KeyValuePair<int, Vector3>> gemList;

        public EventGameGemAchive(List<KeyValuePair<int, Vector3>> _gemList)
        {
            gemList = _gemList;
        }
    }

    // 新增：木箱收集事件
    public struct EventGameWoodenCrateAchive : IEvent
    {
        public int crateCount;
        public Vector3 sourcePosition;

        public EventGameWoodenCrateAchive(int count, Vector3 position)
        {
            crateCount = count;
            sourcePosition = position;
        }
    }

    // 新增：鸟收集事件
    public struct EventGameBirdAchive : IEvent
    {
        public int birdCount;
        public Vector3 sourcePosition;

        public EventGameBirdAchive(int count, Vector3 position)
        {
            birdCount = count;
            sourcePosition = position;
        }
    }

    // 新增：猫收集事件
    public struct EventGameCatAchive : IEvent
    {
        public int catCount;
        public Vector3 sourcePosition;

        public EventGameCatAchive(int count, Vector3 position)
        {
            catCount = count;
            sourcePosition = position;
        }
    }

    // 新增：树叶收集事件
    public struct EventGameLeafAchive : IEvent
    {
        public int leafCount;
        public Vector3 sourcePosition;

        public EventGameLeafAchive(int count, Vector3 position)
        {
            leafCount = count;
            sourcePosition = position;
        }
    }

    // 新增：冰块收集事件
    public struct EventGameIceAchive : IEvent
    {
        public int iceCount;
        public Vector3 sourcePosition;

        public EventGameIceAchive(int count, Vector3 position)
        {
            iceCount = count;
            sourcePosition = position;
        }
    }

    public struct EventGameCoinAchive : IEvent
    {
        public int coinCount;

        public EventGameCoinAchive(int count)
        {
            coinCount = count;
        }
    }

    public struct EventOnApplicationPause : IEvent
    {
        public bool pause;

        public EventOnApplicationPause(bool inPause)
        {
            pause = inPause;
        }
    }

    public struct GameItemChangeEvent : IEvent
    {
        public int itemId;

        public GameItemChangeEvent(int inItemId)
        {
            itemId = inItemId;
        }
    }

    public struct EventGameScoreChange : IEvent
    {
        public int current;
        public int delta;

        public EventGameScoreChange(int inCurrent, int inDelta)
        {
            current = inCurrent;
            delta = inDelta;
        }
    }

    public struct EventProductBlockInfo : IEvent
    {
        public EnumBlockProductType targetType;
        public EnumBlockProductType realType;

        public EventProductBlockInfo(EnumBlockProductType tType, EnumBlockProductType rType)
        {
            targetType = tType;
            realType = rType;
        }
    }

    public struct EventBlockPutDownInfo : IEvent
    {
        public int combolNum;
        public int matchCount;
        public int matchIndex;
        public int Score;
        public EventBlockPutDownInfo(int num, int matchNum,int index,int score =0)
        {
            combolNum = num;
            matchCount = matchNum;
            matchIndex = index;
            Score = score;
        }
    }

    public struct EventBlockClick : IEvent
    {
        public int SelectIndex;
        public EventBlockClick(int index)
        {
            SelectIndex = index;
        }
    }

    public struct EventPlayBombExplode : IEvent
    {
        public Vector3 tPos;

        public EventPlayBombExplode(Vector3 pos)
        {
            tPos = pos;
        }
    }

    public struct EventEnergyChange : IEvent
    {
        public int current;
        public int delta;
        public bool isInfinite;

        public EventEnergyChange(int inCurrent, int inDelta, bool inIsInfinite)
        {
            current = inCurrent;
            delta = inDelta;
            isInfinite = inIsInfinite;
        }
    }

    public struct ResChangeEvent : IEvent
    {
        public EItemType EItemType { get; private set; }

        public bool AnimateChange;

        /// <summary>
        /// 需要忽略的数字大小，输入正数才需要减去该数字，反之则加
        /// </summary>
        public int IgnoreNumber { get; private set; }

        public ResChangeEvent(EItemType cId, int ignoreNumber = 0, bool animateChange = true)
        {
            EItemType = cId;
            IgnoreNumber = ignoreNumber;
            AnimateChange = animateChange;
        }
    }

    public struct EventResolveConflict : IEvent
    {
    }

    public struct SignedSuccessEvent : IEvent
    {
        public bool sign;

        public SignedSuccessEvent(bool inSign)
        {
            sign = inSign;
        }
    }

    public struct EventFaqQuestionServerBack : IEvent
    {
        public int id;

        public EventFaqQuestionServerBack(int inId)
        {
            id = inId;
        }
    }

    public struct EventRestorePurchasesSuccess : IEvent
    {
        public bool pause;

        public EventRestorePurchasesSuccess(bool inPause)
        {
            pause = inPause;
        }
    }

    public struct EventToggleHomeUIState : IEvent
    {
        public bool enable;

        public EventToggleHomeUIState(bool inEnable)
        {
            enable = inEnable;
        }
    }

    public struct EventTryShowGuideTip : IEvent
    {
    }

    public struct EventCurrencyChange : IEvent
    {
        public EItemType EItemType;
        public bool playAni;
        public int delta; //玩家金币变化量

        public EventCurrencyChange(EItemType inEItemType, bool inPlayAni, int inDelta)
        {
            EItemType = inEItemType;
            playAni = inPlayAni;
            delta = inDelta;
        }
    }

    public struct EventAcrossDay : IEvent
    {
    }

    public struct EventLanguageChange : IEvent
    {
    }

    public struct EventFaqSelectQuestion : IEvent
    {
        public int id;

        public EventFaqSelectQuestion(int inId)
        {
            id = inId;
        }
    }

    public struct EventCurrencyFlyAniEnd : IEvent
    {
        public EItemType EItemType;

        public EventCurrencyFlyAniEnd(EItemType inEItemType)
        {
            EItemType = inEItemType;
        }
    }

    public struct EventActiveChange : IEvent
    {
    }

    public struct EventAdBox : IEvent
    {
    }

    public struct IAPSuccess : IEvent
    {
        public DragonPlus.Config.Global.Table_Global_Shop TableGameShopConfig;
        public Object userData;

        public IAPSuccess(DragonPlus.Config.Global.Table_Global_Shop inTableGameShopConfig, Object inObject)
        {
            TableGameShopConfig = inTableGameShopConfig;
            userData = inObject;
        }
    }

    public struct IAPSuccessPopupAfter : IEvent
    {
        public DragonPlus.Config.Global.Table_Global_Shop TableGameShopConfig;
        public Object userData;

        public IAPSuccessPopupAfter(DragonPlus.Config.Global.Table_Global_Shop inTableGameShopConfig, Object inObject)
        {
            TableGameShopConfig = inTableGameShopConfig;
            userData = inObject;
        }
    }

    public struct IAPFailure : IEvent
    {
        public DragonPlus.Config.Global.Table_Global_Shop TableGameShopConfig;

        public IAPFailure(DragonPlus.Config.Global.Table_Global_Shop inTableGameShopConfig)
        {
            TableGameShopConfig = inTableGameShopConfig;
        }
    }

    public struct WindowOpenEvent : IEvent
    {
    }

    public struct WindowCloseEvent : IEvent
    {
    }

    public struct WindowOpenCompleteEvent : IEvent
    {
    }

    public struct WindowCloseCompleteEvent : IEvent
    {
    }

    public struct EvtBindFacebook : IEvent
    {
        public bool isSuccess;

        public EvtBindFacebook(bool isSuccess)
        {
            this.isSuccess = isSuccess;
        }
    }


    public struct FinishGuideEvent : IEvent
    {
        public string GuideId;

        public FinishGuideEvent(string guideId)
        {
            GuideId = guideId;
        }
    }


    #region Activity

    public struct EventActivityCreate : IEvent
    {
        public string activityType;

        public EventActivityCreate(string inActivityType)
        {
            activityType = inActivityType;
        }
    }

    public struct EventActivityExpire : IEvent
    {
        public string activityType;
        public string activityId;

        public EventActivityExpire(string inActivityType, string inActivityId)
        {
            activityType = inActivityType;
            activityId = inActivityId;
        }
    }

    public struct EventActivityUpdate : IEvent
    {
        public string activityType;

        public EventActivityUpdate(string inActivityType)
        {
            activityType = inActivityType;
        }
    }

    public struct EventActivityOnCreate : IEvent
    {
        public string activityType;

        public EventActivityOnCreate(string inActivityType)
        {
            activityType = inActivityType;
        }
    }

    public struct EventActivityEntrance : IEvent
    {
        public string activityType;

        public EventActivityEntrance(string inActivityType)
        {
            activityType = inActivityType;
        }
    }

    public struct EventDailyTask : IEvent
    {
    }

    public struct EventDailyTaskView : IEvent
    {
    }

    public struct EventEndlessGiftPackClick : IEvent
    {
        public int id;

        public EventEndlessGiftPackClick(int value)
        {
            id = value;
        }
    }

    public struct EventStarChallengeRefresh : IEvent
    {
    }

    public struct EventCollectGuildRefresh : IEvent
    {
    }

    public struct EventBuyRechargeGiftPackSuccess : IEvent
    {
    }

    #endregion
    
    /// <summary>
    /// 当棋盘上生成奖励物品时分发
    /// </summary>
    public struct EventRewardItemSpawned : IEvent
    {
        public readonly RewardItemData Item;
        public EventRewardItemSpawned(RewardItemData item) { this.Item = item; }
    }

    /// <summary>
    /// 当奖励物品倒计时更新时分发
    /// </summary>
    public struct EventRewardItemUpdated : IEvent
    {
        public readonly RewardItemData Item;
        public EventRewardItemUpdated(RewardItemData item) { this.Item = item; }
    }

    /// <summary>
    /// 当奖励物品从棋盘上移除时分发
    /// </summary>
    public struct EventRewardItemRemoved : IEvent
    {
        public readonly int Position;
        public readonly bool Collected; // True代表被收集, false代表倒计时结束而消失

        public EventRewardItemRemoved(int position, bool collected)
        {
            this.Position = position;
            this.Collected = collected;
        }
    }
}
