using System.Collections.Generic;
using System.Runtime.CompilerServices;
using Newtonsoft.Json;
using UnityEngine;
using UnityEngine.UI;
using ModGameDefine.Target;

public class levelLayout
{
    public BlockGridInfo[] tiles;
    public LevelTargetInfo targets;
}
/// <summary>
/// 游戏运行时覆盖物数据结构
/// </summary>
[System.Serializable]
public class GameOverlayData
{
    public int id;      // 覆盖物ID
    public int layers;  // 覆盖物层数

    public GameOverlayData() { }

    public GameOverlayData(int id, int layers)
    {
        this.id = id;
        this.layers = layers;
    }
}
public class RewardItemData
{
    public int position; // 物品所在的棋盘格子索引
    public int typeId;   // 奖励物品的类型ID (未来可扩展不同奖励)
    public int countdown; // 剩余操作次数
}
public class BlockGridInfo
{
    public int x;
    public int y;
    public int color;
    public int gem;
    public int clearRemindTime = 0; //剩余可消除次数 （大于0代表有多层）
    public int overlayLevel; //大于0代表上方有遮挡物
    public int overlayLayer = 0; //覆盖物层数（用于多层覆盖物）
    public bool isFixedBlock;//true代表有固定方块（鸟窝）
    public int advancedBlockId = 0;//高级方块ID

    // 新格式字段 - 支持新的配置格式
    public string baseType = "color"; // 方块基础类型："color", "gem", "other"
    public bool isFixed = false; // 固定方块标识（新字段名）
    public int blockLayers = 0; // 方块层数（新字段名）
    public int overlayId = 0; // 覆盖物ID（新字段名，过渡格式）
    public int overlayLayers = 0; // 覆盖物层数（新字段名，过渡格式）

    // 最终重构格式字段 - 支持多层覆盖物叠加
    public List<GameOverlayData> overlays; // 覆盖物数组（最终格式）
    [JsonIgnore]
    public EnumGridColor gridColor
    {
        get { return (EnumGridColor)color; }
        set
        {
            color = (int)value;
        }
    }

    [JsonIgnore]
    public GemType gridGem
    {
        get { return (GemType)gem; }
        set
        {
            gem = (int)value;
        }
    }

    [JsonIgnore]
    public int multiBlockClearTime
    {
        get { return clearRemindTime; }
        set
        {
            clearRemindTime = value;
        }
    }

    [JsonIgnore]
    public int overlayBlockId
    {
        get { return overlayLevel; }
        set
        {
            overlayLevel = value;
        }
    }

    [JsonIgnore]
    public int overlayBlockLayer
    {
        get { return overlayLayer; }
        set
        {
            overlayLayer = value;
        }
    }

    [JsonIgnore]
    public bool fixedBlock
    {
        get { return isFixedBlock; }
        set
        {
            isFixedBlock = value;
        }
    }

    [JsonIgnore]
    public int AdvancedBlockId
    {
        get { return advancedBlockId; }
        set { advancedBlockId = value; }
    }
}

public struct LevelPlayInfo
{
    public static LevelPlayInfo defaultInfo = new LevelPlayInfo(0);

    public int gameResult;
    public int levelId;
    public int enterCount;
    public int rankRadio;

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public LevelPlayInfo(int levelId)
    {
        this.levelId = levelId;
        gameResult = (int)EnumGameResultType.EFST_None;
        enterCount = 0;
        rankRadio = 0;
    }
}

public struct GenBlockInfo
{
    public static GenBlockInfo defaultInfo = new GenBlockInfo(-1);

    public int blockConfig;
    public List<int> gemList;
    public List<int> gemPosList;
    public int blockColor;

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public GenBlockInfo(int config)
    {
        this.blockConfig = config;
        blockColor = -1;
        gemList = null;
        gemPosList = null;
    }

}

#region blockgame类型
public enum EnumBlockGameType
{
    BlockGame_None = -1,
    BlockGame_Endless,     //无尽模式
    BlockGame_Stage,       //关卡模式
}
#endregion

public enum EnumFeelStageType
{
    EFST_None = 0,
    EFST_Relaxed = 1,
    EFST_Normal,
    EFST_Stress,

    EFST_End
}

/// <summary>
/// 出块类型
/// </summary>
public enum EnumBlockProductType
{
    EBPT_None = -1,    //无效
    EBPT_Guarantee = 0,    //保底
    EBPT_ComplexityUp,  //构筑-复杂度提升
    EBPT_ComplexityDown,//消除-复杂度下降
    EBPT_Normal,    //普通随机
    EBPT_Hard,          //难题
    EBPT_MultiClear,          //多消
    EBPT_ClearScreen,          //清屏
    EBPT_Clear,       //消除
    EBPT_EasyHard, //简单难题
    
    EBPT_Revive = 100,    //重生出块
    EBPT_LoadStorage,    //从存储库加载
    EBPT_NoNeed,        //不需要策略出块(从配置中获取,或者其他途径)
    EBPT_PreBack = 200,    //回退
}

public enum EnumGameResultType
{
    EFST_None = -1,
    EFST_Fail,
    EFST_Victory,
    EFST_NoResult,
}

/// <summary>
/// 关卡游戏类型（区分分数关和目标关）
/// </summary>
public enum EnumLevelGameType
{
    ELT_Score = 0,    // 分数关：只要分数达标即可通关
    ELT_Target,       // 目标关：需要所有目标都完成才能通关
}

/// <summary>
/// 关卡模式下目标类型
/// </summary>
public enum EnumTargetType
{
    ETT_Invaild = 0,
    ETT_Gem,
    ETT_Score,
    ETT_WoodenCrate,  // 木箱目标
    ETT_Bird,         // 鸟目标
    ETT_Cat,          // 猫目标
    ETT_Leaf,         // 树叶目标
    ETT_Ice           // 冰块目标
}

public enum EnumGridState
{
    EGS_Hide = 0,   //隐藏状态
    EGS_Alpha,      //半透提示状态
    EGS_Show,       //展示状态
}

//宝石类型
public enum GemType { None, Gem1, Gem2, Gem3, Gem4, Gem5, GemEnd }

// LeafBlock覆盖物配置常量
public static class OverlayConstants
{
    public static readonly Vector2 LeafBlockSize = new Vector2(86, 86);
    // 覆盖物ID常量
    public const int OverlayIdLeaf = 501;
    public const int OverlayIdIce = 502;
    // 冰块层数阈值常量
    public const int IceLayerLow = 1;
    public const int IceLayerMid = 2;
    public const int IceLayerHigh = 3;
}

public enum EnumGridColor
{
    EGC_None = 0,
    EGC_Red = 1,   //红色
    EGC_Orange,      //橙色
    EGC_Yellow,       //黄色
    EGC_Green,       //绿色
    EGC_SkyBlue,       //天蓝色
    EGC_DarkBlue,   //深蓝色
    EGC_Purple,      //紫色
    EGC_End = EGC_Purple,
    EGC_GemBg = 9,  //带宝石的底块的颜色

}
public struct GenerateParam
{
    public static readonly GenerateParam invaildP = new GenerateParam(EnumBlockProductType.EBPT_None, 0, EnumBlockProductType.EBPT_None, 0);

    public static readonly GenerateParam defaultP = new GenerateParam(EnumBlockProductType.EBPT_Normal, 700, EnumBlockProductType.EBPT_Guarantee, 0);

    public EnumBlockProductType pType;
    public int execMilliSecond;
    public EnumBlockProductType pType1;
    public int execMilliSecond1;

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    public GenerateParam(
        EnumBlockProductType type,
        int milliSecond,
        EnumBlockProductType type1,
        int milliSecond1
        )
    {
        this.pType = type;
        this.execMilliSecond = milliSecond;
        this.pType1 = pType;
        this.execMilliSecond1 = milliSecond1;
    }

    public static GenerateParam GetNormalParam(EnumBlockProductType type)
    {
        var theRes = defaultP;
        theRes.pType = type;
        theRes.pType1 = EnumBlockProductType.EBPT_Normal;
        theRes.execMilliSecond1 = 300;
        if (type == EnumBlockProductType.EBPT_Normal)
        {
            theRes.pType1 = EnumBlockProductType.EBPT_Guarantee;
        }
        return theRes;
    }
}

public struct OpenGameInfo
{
    public EnumBlockGameType enumBlockGameType;
    public RectTransform blockPool;
    public RectTransform gridContainer;
    public Transform tipPlay;
    public EnumTargetType enumTargetType;
    public int levelId;
}

public struct BlockMatchMatrix
{
#if DEBUG || DEVELOPMENT_BUILD
    public string strBlockName;
#endif

    public int rowNum;
    public int colNum;
    public int gridCount;
    public uint curLayout;
    public bool haveEmpty;
    public List<int> checkRowEdges;
    public List<int> checkColEdges;
    public List<ulong> curLayouts;
    public List<ulong> curLayouts_col;
    public List<Edge> rowEdges;
    public List<Edge> colEdges;
}

public class BlockGrid
{
    RectTransform rectTf;
    Image curImage;

    public int pos;


    public Image CurImage => curImage;
    public Transform gemTs { get; private set; }
    GemType gemType = GemType.None;

    private EnumGridColor color;

    public EnumGridColor ColorState
    {
        get { return color; }
        set
        {
            color = value;
            if (color == EnumGridColor.EGC_None) color = EnumGridColor.EGC_Red;
            int colorInt = (int)color;
            if (gemType == GemType.None)
            {
                if (color > EnumGridColor.EGC_End)
                {
                    color = EnumGridColor.EGC_Red;
                    colorInt = (int)color;
                }
            }
            else
            {
                if (color != EnumGridColor.EGC_GemBg)
                {
                    color = EnumGridColor.EGC_GemBg;
                    colorInt = (int)color;
                }
            }

            var atlaspath = Const_Common.GameAtlas;
            var iconPath = $"bm_game_Block{colorInt}";
            CoreUtils.SetImg(curImage, atlaspath, iconPath);
        }
    }

    public RectTransform RectTf
    {
        get
        {
            return rectTf;
        }
        set
        {
            rectTf = value;
            curImage = rectTf.GetComponent<Image>();
        }
    }

    public GemType GemType
    {
        get { return gemType; }
        set
        {
            gemType = value;
            RefreshGem();
        }
    }

    void RefreshGem()
    {
        if (rectTf == null) return;

        Image gemIcon = null;
        var theName = "gem";
        var theGem = rectTf.transform.Find(theName);
        var thGemId = (int)gemType;
        if (thGemId > (int)GemType.None && thGemId < (int)GemType.GemEnd)
        {
            if (theGem == null)
            {
                var theGo = rectTf.transform.CreateChild(theName);
                theGem = theGo.transform;
                theGem.localPosition = Vector3.zero;
                theGem.localScale = Vector3.one;
                theGem.parent = rectTf.transform;
            }
            gemIcon = theGem.gameObject.GetComponentOrAdd<Image>();
            if (theGem == null) theGem = gemIcon.transform;
            var iconPath = $"bm_game_small_gem{thGemId}";
            CoreUtils.SetImg(gemIcon, Const_Common.GameAtlas, iconPath);

            gemIcon.rectTransform.sizeDelta = new Vector2(86, 86);
            //gemIcon.SetNativeSize();
            gemTs = theGem;
            theGem?.gameObject.SetActive(true);

            ColorState = EnumGridColor.EGC_GemBg;
        }
        else
        {
            theGem?.gameObject.SetActive(false);
        }

    }
}
