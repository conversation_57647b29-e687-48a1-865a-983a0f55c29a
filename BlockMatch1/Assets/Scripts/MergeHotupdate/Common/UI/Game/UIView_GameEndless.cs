using DG.Tweening;
using Framework;
using TMGame;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using DragonPlus;
using TMGame.Storage;
using DragonPlus.Core;
using DragonPlus.Save;
using DragonU3DSDK.Network.API.Protocol;
using System.Collections.Generic;
using System.Text;
using DragonPlus.Config.InGame;
using System.Threading;
using TMPro;

public class UIView_GameEndless : UIView_GameEndlessBase
{

    private LocalizeTextMeshProUGUI textScore;
    private ModGame gameSys;

    protected Transform UINode_CombolingScoreEffect;
    protected Transform UINode_CombolingScoreEffectForShake;
    protected Transform UINode_CombolingBoardEffect;
    protected Transform UINode_CombolingFullEffect;

    Transform UINode_ClickEffect;
    EnumGameResultType gameResultType = EnumGameResultType.EFST_None;
    bool bCanPlay = false;
    private Animation shakeAnim;
    private CoroutineHandler _shakeEffectCoroutine;
    private int _endlessReviveNeedScore = 1000; //无尽模式复活需要分数
    protected override void BindComponent()
    {
        base.BindComponent();

        gameSys = GameGlobal.GetMod<ModGame>();
        shakeAnim = this.GO.GetComponent<Animation>();

        textScore = UINode_Score.Find("UITex_Level").GetComponent<LocalizeTextMeshProUGUI>();
        UINode_ClickEffect = UINode_Main.Find("VFX_Click");
        UINode_ClickEffect.SetAsLastSibling();
        UINode_ClickEffect.gameObject.SetActive(false);
        UINode_CombolingScoreEffect = UINode_Score.Find("VFX_Score_box");
        UINode_CombolingScoreEffectForShake = UINode_Score.Find("VFX_Score_box2");
        UINode_CombolingBoardEffect = UINode_Main.Find("VFX_checkerboard_light_1");
        UINode_CombolingFullEffect = GO.transform.Find("Root/VFX_checkerboard_light_2");

        TMUtility.NotchAdapte(UINode_Main);

        if (gameSys.DragAreas.Count <= 0)
        {
            gameSys.DragAreas.Add(UINode_Area1);
            gameSys.DragAreas.Add(UINode_Area2);
            gameSys.DragAreas.Add(UINode_Area3);
        }

        if (gameSys.BlockContainers.Count <= 0)
        {
            for (int i = 0; i < UINode_Block1.childCount; i++)
            {
                UINode_Block1.GetChild(i).gameObject.SetActive(false);
            }

            for (int i = 0; i < UINode_Block2.childCount; i++)
            {
                UINode_Block2.GetChild(i).gameObject.SetActive(false);
            }

            for (int i = 0; i < UINode_Block3.childCount; i++)
            {
                UINode_Block3.GetChild(i).gameObject.SetActive(false);
            }

            gameSys.BlockContainers.Add(UINode_Block1);
            gameSys.BlockContainers.Add(UINode_Block2);
            gameSys.BlockContainers.Add(UINode_Block3);

            gameSys.BlockContainerPos.Add(UINode_Block1.position);
            gameSys.BlockContainerPos.Add(UINode_Block2.position);
            gameSys.BlockContainerPos.Add(UINode_Block3.position);
        }

#if DEVELOPMENT_BUILD
        UIBtn_Debug?.gameObject.SetActive(true);
        UITxt_Complex?.gameObject.SetActive(true);
#else
    UINode_Debug.gameObject.SetActive(false);
     UIBtn_Debug?.gameObject.SetActive(false);
    UITxt_Complex?.gameObject.SetActive(false);
#endif

        adReviveCount = 0;
    }

    protected override void RegisterGameEvent()
    {
        base.RegisterGameEvent();
        EventBus.Subscribe<ExitGameEvent>(onExitGame);
        EventBus.Subscribe<ReplayGameEvent>(onReplayGame);
        EventBus.Subscribe<EventGameScoreChange>(HandleRewardScore);
        EventBus.Subscribe<EventProductBlockInfo>(HandleProductBlockEvent);
        EventBus.Subscribe<EventBlockPutDownInfo>(HandleBlockPutDown);
        EventBus.Subscribe<BlockGameOver>(onGameOver);
        EventBus.Subscribe<EventBlockClick>(OnSelectBlock);

    }

    protected override void RemoveGameEvent()
    {
        base.RemoveGameEvent();
        EventBus.Unsubscribe<ExitGameEvent>(onExitGame);
        EventBus.Unsubscribe<ReplayGameEvent>(onReplayGame);
        EventBus.Unsubscribe<EventGameScoreChange>(HandleRewardScore);
        EventBus.Unsubscribe<EventRewardItemSpawned>(OnRewardItemSpawned);
        EventBus.Unsubscribe<EventRewardItemUpdated>(OnRewardItemUpdated);
        EventBus.Unsubscribe<EventRewardItemRemoved>(OnRewardItemRemoved);
        EventBus.Unsubscribe<EventProductBlockInfo>(HandleProductBlockEvent);
        EventBus.Unsubscribe<EventBlockPutDownInfo>(HandleBlockPutDown);
        EventBus.Unsubscribe<BlockGameOver>(onGameOver);
        EventBus.Unsubscribe<EventBlockClick>(OnSelectBlock);

    }

    protected override void RegisterUIEvent()
    {
        base.RegisterUIEvent();

        UIBtn_Close.onClick.AddListener(OnCloseBtn);
        UIBtn_SetUp.onClick.AddListener(OnSetBtn);
        var mFullShield = UINode_FullShield;
        UIUtils.AddEventTrigger(mFullShield.gameObject, EventTriggerType.PointerDown, OnPointerDown);
        UIUtils.AddEventTrigger(mFullShield.gameObject, EventTriggerType.PointerUp, OnPointerUp);
        UIUtils.AddEventTrigger(mFullShield.gameObject, EventTriggerType.Drag, OnDrag);
        UIUtils.AddEventTrigger(mFullShield.gameObject, EventTriggerType.BeginDrag, OnBeginDrag);
        UIUtils.AddEventTrigger(mFullShield.gameObject, EventTriggerType.EndDrag, OnEndDrag);

        UIBtn_ComplexityUp.onClick.AddListener(OnComplexityUpBtn);
        UIBtn_ComplexityDown.onClick.AddListener(OnComplexityDownBtn);
        UIBtn_Hard.onClick.AddListener(OnHardBtn);
        UIBtn_Random.onClick.AddListener(OnRandomBlockBtn);
        UIBtn_MultiClear.onClick.AddListener(() =>
        {
            gameSys.Debug_GenerateBlockGo(EnumBlockProductType.EBPT_MultiClear);
        });
        UIBtn_ClearScreen.onClick.AddListener(() =>
        {
            gameSys.Debug_GenerateBlockGo(EnumBlockProductType.EBPT_ClearScreen);
        });

        UIBtn_Debug.onClick.AddListener(OnShowDebugBtn);
    }

    CancellationTokenSource baseBoardCts;
    int tempLastBaseBoardIndex = -1;

    protected async override void OnOpen()
    {
        base.OnOpen();
        tempLastBaseBoardIndex = -1;
        gameStorage.EnterCount += 1;
        onOpenGame();

        int step = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep;
        if (step < 2)
        {
            gameSys.HandleEndlessLayoutForGuide(step);
        }
        else
        {
            //string playerIdStr = SDKUtil.Misc.PlayerIdToString(SDK<IStorage>.Instance.Get<StorageCommon>().PlayerId);
            if (gameStorage.GameResult != (int)EnumGameResultType.EFST_NoResult)
            {
                var abTestBaseBoard = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.ABTestBaseBoardNew);
                var curEnterIndex = gameStorage.TodayPlayCount;
                CLog.Info(
                    $"BaseBoard---curEnterIndex:{curEnterIndex},LastBaseBoardIndex:{gameStorage.LastBaseBoardIndex}");
                
                if (curEnterIndex - gameStorage.LastBaseBoardIndex >= 1 && abTestBaseBoard == EABTestGroup.Group1)
                {
                    var theRandom = UnityEngine.Random.Range(0, 2);
                    CLog.Info($"BaseBoard---theRandom:{theRandom}");
                    if (theRandom == 0)
                    {
                        var theBestScore = StorageExtension.GameEndlssStorage.CurFirstScore;
                        var theConfig = InGameConfigManager.GetBaseBoardConfig(theBestScore);
                        if (theConfig != null && theConfig.Sparsity?.Count == 2 && theConfig.RowColumn?.Count == 2)
                        {
                            var theExecTime = 900;
                            baseBoardCts = new CancellationTokenSource();
                            baseBoardCts.CancelAfter(theExecTime);

                            var theMaxComplex = theConfig.Complextiy;
                            Vector2Int sparsityRange = new Vector2Int(theConfig.Sparsity[0], theConfig.Sparsity[1]);
                            Vector2Int rowColRange = new Vector2Int(theConfig.RowColumn[0], theConfig.RowColumn[1]);
                            var theTask = gameSys.HandleBaseBoard(baseBoardCts.Token, theMaxComplex, sparsityRange,
                                rowColRange);
                            await theTask;

                            if (baseBoardCts != null)
                            {
                                if (theTask.Result) tempLastBaseBoardIndex = curEnterIndex;
                                baseBoardCts.Cancel();
                                baseBoardCts.Dispose();
                                baseBoardCts = null;
                            }
                        }
                    }
                }
            }
        }

        BIHelper.ELevelInfoType tempType = BIHelper.ELevelInfoType.LoadStorage;
        var theStorage = StorageExtension.GameEndlssStorage;
        if (theStorage.GameResult == (int)EnumGameResultType.EFST_Fail
            || theStorage.GameResult == (int)EnumGameResultType.EFST_Victory)
        {
            tempType = BIHelper.ELevelInfoType.Enter;
        }

        BIHelper.SendLevelInfo(tempType, EnumBlockGameType.BlockGame_Endless,
            gameSys.PassTime, 0, 0, 0,
            gameSys.CurHardProductCount, gameSys.MaxMatchClearCount, 0, gameSys.CurProductType
            , (int)BlockPlayManager.Instance.Complexity, 0, CurReviveCount, gameStorage.EnterCount,
            gameSys.GenRoundIndex, gameSys.CurScore, gameSys.PlayerActionRecord);


    }

    void onOpenGame()
    {
#if DEBUG || DEVELOPMENT_BUILD
        var watch = new System.Diagnostics.Stopwatch();
        watch.Start();
#endif
        bCanPlay = false;
        blockBoardAni = null;
        isPayBoard = false;
        UINode_Content.gameObject.SetActive(false);
        UINode_Score.gameObject.SetActive(true);
        UINode_Ads.gameObject.SetActive(false);
        UINode_Target.gameObject.SetActive(false);
        UINode_Rank.gameObject.SetActive(false);
        UINode_Down.gameObject.SetActive(false);
        UINode_Tip.gameObject.SetActive(false);
        UINode_PutTip.gameObject.SetActive(false);
        UINode_Debug.gameObject.SetActive(false);
        _endlessReviveNeedScore = InGameConfigManager.GetGlobalConfig<int>("EndlessReviveNeedScore");
        // 连击倒计时初始化（已禁用）
        //combolCd = 0;
        //combolShow = false;
        UITxt_CombolCd.gameObject.SetActive(false);
        UINode_CombolingScoreEffect.gameObject.SetActive(false);
        UINode_CombolingScoreEffectForShake.gameObject.SetActive(false);

        DisposeClearGuide();
        OpenGameInfo theInfo = new OpenGameInfo()
        {
            enumBlockGameType = EnumBlockGameType.BlockGame_Endless,
            blockPool = UINode_BlockPool,
            gridContainer = UINode_Content,
            tipPlay = UINode_TipPlay,
            enumTargetType = EnumTargetType.ETT_Invaild,
        };
        gameSys.OnOpenGame(theInfo);

        var theStorage = StorageExtension.GameEndlssStorage;
        if (theStorage.GameResult == (int)EnumGameResultType.EFST_NoResult)
        {
            textScore.SetText(theStorage.AchiveScore.ToString());
        }
        else
        {
            textScore.SetText("0");
        }

        showTipInfo();

        gameResultType = EnumGameResultType.EFST_None;

#if DEBUG || DEVELOPMENT_BUILD
        var thePlan = gameSys.CurBlockProductIsNative ? "Native" : "Local";
        UITxt_BlockPlan.SetText(thePlan);
        thePlan = gameSys.CurBlockIsNewOrder ? "MRL" : "LMR";
        UITxt_BlockOrder.SetText(thePlan);

        watch.Stop();
        CLog.Info($"GameEndless.OnOpenGame,花费时间{watch.ElapsedMilliseconds}毫秒");
#endif
    }


    protected override void OnOpenAniComplete()
    {
        base.OnOpenAniComplete();
        GameGlobal.GetMgr<SoundMgr>().PlayBgMusic("bg_level_in_1");
    }

    protected override void OnClose()
    {
        base.OnClose();
        HandleDispose();
        GameGlobal.GetMgr<SoundMgr>().PlayBgMusic("bg_level_out_1");
    }

    protected override void OnUpdate()
    {
        base.OnUpdate();

        if (!bCanPlay || this.GO == null) return;

        // 连击倒计时更新逻辑（已禁用）
        // 注意：UINode_CombolingScoreEffect现在由HandleBlockPutDown中的连击状态直接控制
        /*
        if (combolShow && UITxt_CombolCd && UINode_CombolingScoreEffect)
        {
            if (combolCd == 0)
            {
                combolShow = false;
                UITxt_CombolCd.gameObject.SetActive(false);
                UINode_CombolingScoreEffect.gameObject.SetActive(false);
            }
            else
            {
                UINode_CombolingScoreEffect.gameObject.SetActive(true);
#if UNITY_EDITOR
                UITxt_CombolCd.SetText(combolCd.ToString());
#endif
                secondLogicValue += Time.deltaTime;
                if (secondLogicValue >= 1.0f)
                {
                    secondLogicValue = 0;
                    combolCd -= 1;
                }
            }
        }
        */

    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        HandleDispose(true);

        var guildGroup = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.ABTestGuide);
        BIHelper.SendGameEvent(guildGroup == EABTestGroup.Group2
            ? BiEventBlockMatch1.Types.GameEventType.GameEventFteTestReturnHome
            : BiEventBlockMatch1.Types.GameEventType.GameEventFteReturnHome);
    }

    private void OnCloseBtn()
    {
        HandleLevelInfoBI(EnumGameResultType.EFST_NoResult);
        Close(true, () => { EventBus.Dispatch(new ExitBlockPlayEvent(1)); });

    }

    private void OnSetBtn()
    {
        GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_GameSetting, new GameSettingData());
    }

    #region 分数更新

    // 连击倒计时相关变量（已禁用）
    //float secondLogicValue = 0;
    //bool combolShow = false;
    //float combolCd = 0;

    void HandleBlockPutDown(EventBlockPutDownInfo evt)
    {
        if (gameResultType == EnumGameResultType.EFST_None)
        {
            gameResultType = EnumGameResultType.EFST_NoResult;
        }

        RefreshComplexlnfo();
        var theMatchCount = evt.matchCount;
        bool bHaveShake = false;
        if (theMatchCount > 0)
        {
            // 震屏效果（多消不播放震屏，连击才播）
            // if (theMatchCount >= 3)
            // {
            //     bHaveShake = true;
            //     ScreenVibrationManager.Instance.SetVibration(false, 0, ViewRootRect, shakeAnim);
            // }
            // else
            {
                if (evt.combolNum >= 3)
                {
                    bHaveShake = true;
                    ScreenVibrationManager.Instance.SetVibration(true, evt.combolNum, ViewRootRect, shakeAnim);
                }

                if (evt.combolNum >= 3)
                {
                    UINode_CombolingBoardEffect.gameObject.SetActive(false);
                    UINode_CombolingFullEffect.gameObject.SetActive(false);
                    UINode_CombolingBoardEffect.gameObject.SetActive(true);
                    UINode_CombolingFullEffect.gameObject.SetActive(true);
                }
            }
        }

        if (bHaveShake)
        {
            if (_shakeEffectCoroutine != null)
            {
                GameGlobal.GetMod<ModCoroutine>().StopCoroutine(_shakeEffectCoroutine);
                UINode_CombolingScoreEffectForShake.gameObject.SetActive(false);
            }

            UINode_CombolingScoreEffectForShake.gameObject.SetActive(true);
            _shakeEffectCoroutine = GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(1,
                () =>
                {
                    _shakeEffectCoroutine = null;
                    UINode_CombolingScoreEffectForShake.gameObject.SetActive(false);
                }));
        }


        // 连击倒计时显示逻辑（已禁用）
        /*
        if (evt.combolNum >= 1)
        {
            if(evt.combolNum == 1 && theMatchCount <= 0)
            {
                combolShow = false;
                UITxt_CombolCd.gameObject.SetActive(false);
            }
            else
            {
                UITxt_CombolCd.gameObject.SetActive(true);
                combolShow = true;
                combolCd = ModGame.CombolInterval;
            }
            
        }
        else
        {
            combolShow = false;
            UITxt_CombolCd.gameObject.SetActive(false);
        }
        */
        UITxt_CombolCd.gameObject.SetActive(false);

        // 连击特效显示逻辑：当combe x 2及以上时显示，否则隐藏
        if (evt.combolNum >= 3)
        {
            UINode_CombolingScoreEffect.gameObject.SetActive(true);
        }
        else
        {
            UINode_CombolingScoreEffect.gameObject.SetActive(false);
        }

        
        if (theMatchCount >= 3)
        {
            UINode_CombolingBoardEffect.gameObject.SetActive(false);
            UINode_CombolingFullEffect.gameObject.SetActive(false);
            UINode_CombolingBoardEffect.gameObject.SetActive(true);
            UINode_CombolingFullEffect.gameObject.SetActive(true);
        }

        StopGuide();
        TryShowNextStep_ForClearSceneGuide();
        int step = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep;
        if (step < 2)
        {
            GameUtils.SetEventSystemEnable(false);
            GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(1f,
                () =>
                {
                    GameUtils.SetEventSystemEnable(true);
                    gameSys.HandleEndlessLayoutForGuide(step);
                    gameSys.HandleEndlessBlockForGuide(step);
                    PlayGuide();
                }));
        }

        if (theMatchCount > 0)
        {
            var theBlockCount = BlockPlayManager.Instance.GetBlockCount();
            if (theBlockCount <= 0)
            {
                //清屏
                PlayBoard(false,evt.Score);
            }
        }

        isTriggerGuide = false;
    }

    void HandleProductBlockEvent(EventProductBlockInfo evt)
    {
        var theType1 = gameSys.GetPruductName(evt.targetType);
        var theType2 = gameSys.GetPruductName(evt.realType);
        UITxt_PType?.SetText($"{theType1}->{theType2}");
    }

    StorageGameEndless gameStorage
    {
        get { return StorageExtension.GameEndlssStorage; }
    }
    // 是否触发了分数清零
    private bool isTriggerScoreClean = false;
    void HandleRewardScore(EventGameScoreChange evt)
    {
        int curScore = evt.current;
        int score = evt.delta;
        //if (score <= 0 || textScore == null) return;
        // 这里去掉了负分的判断，因为需要处理引导期间得分数清零的刷新操作
        if (textScore == null) return;

        int preScore = curScore;
        curScore += score;
        // 玩法引导特殊处理，正常情况分数不会为负
        if(score <= 0)
        {
            //刷新分数显示
            if (gameStorage.CurFirstScore > 0)
            {
                gameStorage.CurFirstScore = 0;
            }
            isTriggerScoreClean = true;
            return;
        }
        //刷新分数显示
        textScore.SetText(curScore.ToString());

        {
            int tempScore = curScore;
            UIUtils.UpdateTextAnimation(textScore, score, curScore, 0.85f, () =>
            {
                if (textScore == null) return;
                if (isTriggerScoreClean)
                {
                    textScore.SetText("0");
                    isTriggerScoreClean = false;
                    return;
                }
                textScore.SetText(tempScore.ToString());
            });
        }

        if (curScore >= gameStorage.CurFirstScore)
        {
            if (showRank != 1)
            {
                showRank = 1;
                UpdateRankInfo(showRank);
            }

            rankScoreText?.SetText(curScore.ToString());
        }
        else if (curScore > gameStorage.CurSecondScore)
        {
            showRank = 1;
            UpdateRankInfo(showRank);
        }
        else if (curScore > gameStorage.CurThirdScore)
        {
            if (showRank == 3)
            {
                showRank = 2;
                UpdateRankInfo(showRank);
            }
        }

        showRank = 1;
        UpdateRankInfo(showRank);

        {
            if (gameSys.CurLife <= 0)
            {
                var lifeCount = 0;
                if(curScore >= _endlessReviveNeedScore)
                {
                    lifeCount = 2;
                }
                // if (gameStorage.CurThirdScore > 0)
                // {
                //     var theHalf = gameStorage.CurThirdScore / 2;
                //     if (preScore < theHalf && curScore >= theHalf)
                //     {
                //         lifeCount = 2;
                //     }
                // }
                // else if (gameStorage.CurSecondScore > 0)
                // {
                //     var theHalf = gameStorage.CurSecondScore / 2;
                //     if (preScore < theHalf && curScore >= theHalf)
                //     {
                //         lifeCount = 2;
                //     }
                // }
                // else if (gameStorage.CurFirstScore > 0)
                // {
                //     var theHalf = gameStorage.CurFirstScore / 2;
                //     if (preScore < theHalf && curScore >= theHalf)
                //     {
                //         lifeCount = 2;
                //     }
                // }

                if (lifeCount > 0)
                {
                    UpdateAdsCount(lifeCount);
                }
            }

        }

        CLog.Info($"HandleRewardScore----{score}, {curScore}");
    }

    void UpdateRankInfo(int showRank)
    {
        if (rankScoreText == null) return;

        bool bUpdateIcon = true;
        switch (showRank)
        {
            case 1:
            {
                rankScoreText.SetText(gameStorage.CurFirstScore.ToString());
                break;
            }
            case 2:
            {
                rankScoreText.SetText(gameStorage.CurSecondScore.ToString());
                break;
            }
            case 3:
            {
                rankScoreText.SetText(gameStorage.CurThirdScore.ToString());
                break;
            }
            default:
            {
                bUpdateIcon = false;
                break;
            }
        }

        if (bUpdateIcon)
        {
            var iconPath = $"bm_icon_trophies{showRank}";
            ;
            CoreUtils.SetImg(rankIcon, Const_Common.GameAtlas, iconPath);
        }
    }

    #endregion

    #region 广告复活相关

    bool UpdateAdsCount(int lifeCount)
    {
        if (lifeCount <= 0) return false;

        var adSys = GameGlobal.GetMod<AdSys>();
        var theAdReady = adSys.IsRewardVideoReady(eAdReward.GameRevive);
        var group = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.abTestEndlessCoinRevive);
        var isCoinRevive = group == EABTestGroup.Group2;
#if UNITY_EDITOR
        theAdReady = true;
#endif
        var rvCount = GameGlobal.GetMod<AdSys>().GetRewardLastCount(eAdReward.GameRevive);
        if (rvCount <= 0)
        {
            return false;
        }

        UINode_Ads.gameObject.SetActive(theAdReady);
        // 只要满足广告和金币复活分组任意条件，都可以增加剩余次数
        // 这个次数虽然是广告复活次数，但是会作为是否可以复活的判断依据
        // 没有时间调整结构了，先这样
        if (theAdReady) gameSys.RewardLife(lifeCount);
        else if (isCoinRevive) gameSys.RewardLife(lifeCount);
        
        UITxt_AdsCount.SetText(gameSys.CurLife.ToString());
        return true;
    }

    public int CurReviveCount => (adReviveCount + gameSys.coinReviveCount);

    public int AdReviveCount => adReviveCount;

    public int CoinReviveCount
    {
        get { return gameSys.coinReviveCount; }
    }

    int adReviveCount = 0;

    public bool HandleReviveByAd()
    {
        if (gameSys.CurLife <= 0) return false;
        if (adReviveCount >= 2) return false;

        gameResultType = EnumGameResultType.EFST_NoResult;
        adReviveCount += 1;
        BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventReliveSuccess, "0");

        gameSys.RewardLife(-1);
        UITxt_AdsCount.SetText(gameSys.CurLife.ToString());
        var theParam = GenerateParam.GetNormalParam(EnumBlockProductType.EBPT_Revive);
        gameSys.GenerateBlockGo(theParam);
        return true;
    }

    public bool HandleReviveByCoin(int payCoin)
    {
        gameResultType = EnumGameResultType.EFST_NoResult;
        gameSys.coinReviveCount += 1;
        BIHelper.SendGameEvent(BiEventBlockMatch1.Types.GameEventType.GameEventReliveSuccess, payCoin.ToString());

        var theParam = GenerateParam.GetNormalParam(EnumBlockProductType.EBPT_Revive);
        gameSys.GenerateBlockGo(theParam);
        return true;
    }


    #endregion

    List<Transform> blockBornEffect = new List<Transform>();
    private CoroutineHandler _blockBornEffectCoroutine;

    public void PlayBlockBornEffect(List<int> noShowList = null)
    {
        if (blockBornEffect.Count <= 0)
        {
            var theChildCount = UINode_BlockBornEffect.childCount;
            for (int i = 0; i < theChildCount; i++)
            {
                blockBornEffect.Add(UINode_BlockBornEffect.GetChild(i));
            }
        }

        UINode_BlockBornEffect.gameObject.SetActive(true);

        for (int i = 0; i < blockBornEffect.Count; i++)
        {
            if (noShowList == null)
            {
                blockBornEffect[i].gameObject.SetActive(true);
            }
            else
            {
                var theIndex = noShowList.FindIndex(ele => ele == i);
                blockBornEffect[i].gameObject.SetActive(theIndex == -1);
            }

        }

        if (_blockBornEffectCoroutine != null)
            GameGlobal.GetMod<ModCoroutine>().StopCoroutine(_blockBornEffectCoroutine);
        _blockBornEffectCoroutine = GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(1,
            () =>
            {
                _blockBornEffectCoroutine = null;
                UINode_BlockBornEffect.gameObject.SetActive(false);
            }));
    }

    #region 展示开始时的提示信息

    GameObject goRankTrailEffect;
    LocalizeTextMeshProUGUI rank1ScoreText;
    LocalizeTextMeshProUGUI rank2ScoreText;
    LocalizeTextMeshProUGUI rank3ScoreText;
    GameButton tipBtn;
    Image rankIcon;
    LocalizeTextMeshProUGUI rankScoreText;
    int showRank = 3;

    void HandleAfterPlayTip(bool isNewPlay = false)
    {
        if (this.GO == null) return;
        UIBtn_Close.enabled = true;
        if (isNewPlay)
        {
            UINode_Rank.gameObject.SetActive(false);
            rankScoreText = UINode_Rank.Find("RankInfo/UITex_Count").GetComponent<LocalizeTextMeshProUGUI>();
        }
        else
        {
            var theCollisionEffect = UINode_Rank.Find("RankInfo/VFX_collision");
            UINode_Rank.gameObject.SetActive(true);
            if (theCollisionEffect) theCollisionEffect.gameObject.SetActive(true);
            rankIcon = UINode_Rank.Find("RankInfo/BG_Rank").GetComponent<Image>();
            rankScoreText = UINode_Rank.Find("RankInfo/UITex_Count").GetComponent<LocalizeTextMeshProUGUI>();
            showRank = 1;
            if (gameStorage.CurThirdScore > 0)
            {
                showRank = 3;
            }
            else if (gameStorage.CurSecondScore > 0)
            {
                showRank = 2;
            }

            showRank = 1;
            UpdateRankInfo(showRank);
        }

        if (!GameGlobal.GetMod<GuideSys>().IsFinished("GUIDE_105"))
        {
            //GameGlobal.GetMod<GuideSys>().RegisterTarget(GuideTargetType.DragBlock,this.RectTransform.transform,"1");
            // GameGlobal.GetMod<GuideSys>().Trigger(GuideTrigger.DragBlock, "1");
        }

        PlayBoard(true);

        var guide = GameGlobal.GetMod<GuideSys>();
        int step = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep;
        if (step < 2)
        {
            gameSys.HandleEndlessLayoutForGuide(step);
            gameSys.HandleEndlessBlockForGuide(step);
        }
        else if (gameStorage.GameResult == (int)EnumGameResultType.EFST_NoResult)
        {
            var theParam = GenerateParam.GetNormalParam(EnumBlockProductType.EBPT_LoadStorage);
            gameSys.GenerateBlockGo(theParam);
        }
        else
        {
            if (baseBoardCts != null)
            {
                baseBoardCts.Cancel();
                baseBoardCts.Dispose();
                baseBoardCts = null;
                GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(0.1f,
                    () => { gameSys.HandleBlockProduct(); }));
            }
            else
            {
                gameSys.HandleBlockProduct();
            }


        }
    }

    Sequence sTipFly;

    void showTipInfo()
    {
        var isNewPlay = gameStorage.CurFirstScore <= 0;
        if (isNewPlay)
        {
            HandleAfterPlayTip(true);
            return;
        }

        UINode_Rank.gameObject.SetActive(false);
        var theCollisionEffect = UINode_Rank.Find("RankInfo/VFX_collision");
        theCollisionEffect.gameObject.SetActive(false);
        UINode_Tip.gameObject.SetActive(true);
        var theGlowEffect = UINode_Tip.Find("Rank/vfx_glow");
        theGlowEffect.gameObject.SetActive(true);

        if (tipBtn == null)
        {
            tipBtn = UINode_Tip.GetComponent<GameButton>();
            tipBtn.onClick.AddListener(() =>
            {
                UINode_Tip.gameObject.SetActive(false);
                UIBtn_Close.enabled = false;
                Transform tempNode = null;
                if (gameStorage.CurThirdScore > 0)
                {
                    tempNode = UINode_Tip.Find("Rank/UICe_RankTip/3Tip");
                }
                else if (gameStorage.CurSecondScore > 0)
                {
                    tempNode = UINode_Tip.Find("Rank/UICe_RankTip/2Tip");
                }
                else if (gameStorage.CurFirstScore > 0)
                {
                    tempNode = UINode_Tip.Find("Rank/UICe_RankTip/1Tip");
                }

                if (tempNode != null)
                {
                    var flyObj = GameGlobal.GetMod<FlySys>().GetFlyClone(tempNode);
                    var theAsset = GameGlobal.GetMgr<ResMgr>().GetRes<GameObject>("VFX_Rank_Trail")
                        .GetInstance(flyObj.gameObject);
                    if (theAsset != null)
                    {
                        var theEffectGo = GameObject.Instantiate(theAsset, flyObj);
                        theEffectGo.transform.localPosition = Vector3.zero;
                    }

                    GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_target_1");
                    sTipFly = GameGlobal.GetMod<FlySys>().NormalFly(flyObj.gameObject, tempNode.position,
                        UINode_Rank.position,
                        false, action: () =>
                        {
                            sTipFly = null;
                            flyObj.gameObject.SetActive(false);
                            GameObject.Destroy(flyObj.gameObject);
                            HandleAfterPlayTip();
                        });
                }
                else
                {
                    HandleAfterPlayTip(true);
                }
            });
        }

        var theRandTipNode = UINode_Tip.Find("Rank/UICe_RankTip");
        if (rank1ScoreText == null || rank2ScoreText == null || rank3ScoreText == null)
        {

            rank1ScoreText = theRandTipNode.Find("1Tip/UITex_Count").GetComponent<LocalizeTextMeshProUGUI>();
            rank2ScoreText = theRandTipNode.Find("2Tip/UITex_Count").GetComponent<LocalizeTextMeshProUGUI>();
            rank3ScoreText = theRandTipNode.Find("3Tip/UITex_Count").GetComponent<LocalizeTextMeshProUGUI>();
        }

        rank1ScoreText.SetText(gameStorage.CurFirstScore.ToString());
        rank2ScoreText.SetText(gameStorage.CurSecondScore.ToString());
        rank3ScoreText.SetText(gameStorage.CurThirdScore.ToString());

        var theNode3 = theRandTipNode.Find("3Tip");
        var theNode2 = theRandTipNode.Find("2Tip");
        if (theNode2 && theNode3)
        {
            theNode2.gameObject.SetActive(false);
            theNode3.gameObject.SetActive(false);
            if (gameStorage.CurThirdScore > 0)
            {
                theNode2.gameObject.SetActive(true);
                theNode3.gameObject.SetActive(true);
            }
            else if (gameStorage.CurSecondScore > 0)
            {
                theNode2.gameObject.SetActive(true);
            }
        }

    }

    Animator blockBoardAni;
    bool isPayBoard = false;

    void PlayBoard(bool isEnterShow = false,int score =0)
    {
        
        if (isPayBoard) return;
        isPayBoard = true;
        bCanPlay = false;
        GameUtils.SetEventSystemEnable(false);
        bool isFirst = !isEnterShow && !StorageExtension.GameEndlssStorage.IsNotFirstPlayClearEffect &&
                       !isTriggerGuide && SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessEnd;
        if (isFirst)
        {
            ModGame.PlayingClearScreen = true;
            blockBoardAni = null;
            if (!isPlayClearGuide)
            {
                //如用户先触发过清屏，则后续不触发清屏引导
                SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.ClearScreenGuide = true;
            }
            UINode_TipPlay.gameObject.SetActive(false);
        }
      
        if (blockBoardAni == null)
        {
            var theNode = isFirst ? UINode_Main.Find("VFX_Block_AllClear") : UINode_Main.Find("VFX_Block");
            blockBoardAni = theNode?.GetComponent<Animator>();
            var textNode = theNode.transform.Find("VFX_AllClear/root/number");
            var chars = score.ToString().ToCharArray();
            StringBuilder sb = new StringBuilder();
            sb.Append("<sprite name=\"+\">");
            // <sprite name="+"><sprite name="1"><sprite name="0"><sprite name="0"><sprite name="!">
            for (int i = 0; i < chars.Length; i++)
            {
                sb.Append($"<sprite name=\"{chars[i]}\">");
            }
            sb.Append("<sprite name=\"!\">");
            if (null !=  textNode)
            {
                textNode.GetComponent<TextMeshProUGUI>().SetText(sb.ToString());
            }
            var textNode2 = theNode.transform.Find("VFX_AllClear/root/number");
            if (null !=  textNode2)
            {
                textNode2.GetComponent<TextMeshProUGUI>().SetText(sb.ToString());
            }

        }

        if (blockBoardAni)
        {
            GameGlobal.GetMgr<SoundMgr>().PlaySfx("block_in_game_1");
            blockBoardAni.gameObject.SetActive(true);
            if (isFirst)
            {
                blockBoardAni.PlayAnim("VFX_Block_AllClear", AfterPlayBoard);
                StorageExtension.GameEndlssStorage.IsNotFirstPlayClearEffect = true;
                blockBoardAni = null;
            }
            else
            {
                blockBoardAni.PlayAnim("VFX_Block", AfterPlayBoard);
            }
        }
        else
        {
           
            AfterPlayBoard();
        }
    }

    void AfterPlayBoard()
    {
        if (UINode_TipPlay.gameObject.activeSelf== false)
        {
            UINode_TipPlay.gameObject.SetActive(true);
        }
        ModGame.PlayingClearScreen = false;
        if (blockBoardAni) blockBoardAni.gameObject.SetActive(false);
        UINode_Content.gameObject.SetActive(true);
        bCanPlay = true;
        isPayBoard = false;
        GameUtils.SetEventSystemEnable(true);

        int step = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep;
        if (step < 2)
        {
            PlayGuide();
        }
    }


    #endregion



    #region UI Event

    void OnPointerDown(BaseEventData baseEventData)
    {
        if (!bCanPlay) return;
        PointerEventData pointerEventData = baseEventData as PointerEventData;
        var thePos = pointerEventData.pointerCurrentRaycast.worldPosition;
        UINode_ClickEffect.gameObject.SetActive(false);
        UINode_ClickEffect.gameObject.SetActive(true);
        UINode_ClickEffect.position = thePos;
        gameSys.OnPointerDown(baseEventData, UINode_Refer, UINode_Content);
    }

    void OnPointerUp(BaseEventData baseEventData)
    {
        if (!bCanPlay) return;
        bool bIsPutDown = false;
        gameSys.OnPointerUp(baseEventData, ref bIsPutDown);
#if DEVELOPMENT_BUILD
        if (bIsPutDown)
        {
            UITxt_Complexlnfo.gameObject.SetActive(true);
        }
#endif
    }

    void OnDrag(BaseEventData baseEventData)
    {
        if (!bCanPlay) return;
        gameSys.OnDrag(baseEventData);
    }

    void OnBeginDrag(BaseEventData baseEventData)
    {
        if (!bCanPlay) return;
        gameSys.OnBeginDrag(baseEventData);
    }

    void OnEndDrag(BaseEventData baseEventData)
    {
        //if (!bCanPlay) return;
        gameSys.OnEndDrag(baseEventData);
    }

    void HandleLevelInfoBI(EnumGameResultType resultType)
    {
        var theType = BIHelper.ELevelInfoType.Quit;
        switch (resultType)
        {
            case EnumGameResultType.EFST_Fail:
                theType = BIHelper.ELevelInfoType.Fail;
                break;
            case EnumGameResultType.EFST_Victory:
                theType = BIHelper.ELevelInfoType.Pass;
                break;
            default:
                break;
        }

        BIHelper.SendLevelInfo(theType, EnumBlockGameType.BlockGame_Endless,
            gameSys.PassTime, 0, 0, 0,
            gameSys.CurHardProductCount, gameSys.MaxMatchClearCount, 0, gameSys.CurProductType
            , (int)BlockPlayManager.Instance.Complexity, 0, CurReviveCount, gameStorage.EnterCount,
            gameSys.GenRoundIndex, gameSys.CurScore, gameSys.PlayerActionRecord);
    }

    void onExitGame(ExitGameEvent evt)
    {
        HandleLevelInfoBI(evt.resType);

        Close(true, () => { EventBus.Dispatch(new ExitBlockPlayEvent(1)); });
    }

    void onReplayGame(ReplayGameEvent evt)
    {
        BlockPlayManager.Instance.HandleReset();

        var theStorage = StorageExtension.GameEndlssStorage;
        theStorage.GameResult = (int)EnumGameResultType.EFST_Fail;
        theStorage.HandleReset();

        onOpenGame();
    }

    void HandleGameOver()
    {
        gameResultType = EnumGameResultType.EFST_Victory;
        int curLife = gameSys.CurLife;

        GameEndlessVictoryData theViewData = new GameEndlessVictoryData();
        theViewData.curFirstScore = gameStorage.CurFirstScore;
        theViewData.curSecondScore = gameStorage.CurSecondScore;
        theViewData.curThirdScore = gameStorage.CurThirdScore;
        theViewData.achievedScore = gameSys.CurScore;
        theViewData.curLife = curLife;

        if (gameSys.IsForbidRewardUI)
        {
            HandleGameReward();
        }
        else
        {
            GameGlobal.GetMgr<UIMgr>().OpenSync(UIViewName.UIView_GameEndlessVictory, theViewData);
        }

        if (curLife <= 0) HandleGameReward(); //不能复活了就直接结算
    }

    void onGameOver(BlockGameOver go)
    {
        var curScore = gameSys.CurScore;
        bool isWin = false;
        if (curScore > gameStorage.CurFirstScore
            || curScore > gameStorage.CurSecondScore
            || curScore > gameStorage.CurThirdScore)
        {
            isWin = true;
        }

        var theTime = 1f;
        if (false == isWin)
        {
            theTime = 1.5f;
            var theTipContent = LocalizationManager.Instance.GetLocalizedString("&key.UI_level_desc_37");
            GameGlobal.GetMod<ModTip>().ShowTip(theTipContent, showDuration: theTime);
        }

        if (tempLastBaseBoardIndex >= 0 && gameResultType != EnumGameResultType.EFST_None)
        {
            gameStorage.LastBaseBoardIndex = tempLastBaseBoardIndex + 1;
            tempLastBaseBoardIndex = -1;
        }

        bCanPlay = false;
        GameUtils.SetEventSystemEnable(false);
        GameGlobal.GetMod<ModCoroutine>().StartCoroutine(TMUtility.DelayWork(theTime,
            () =>
            {
                bCanPlay = true;
                GameUtils.SetEventSystemEnable(true);
                HandleGameOver();
            }));
    }

    #endregion


    public void HandleGameReward()
    {
        gameSys.HandleGameReward(gameResultType);
    }

    void HandleDispose(bool fromDistory = false)
    {
        if (baseBoardCts != null)
        {
            baseBoardCts.Cancel();
            baseBoardCts.Dispose();
            baseBoardCts = null;
        }

        if (_blockBornEffectCoroutine != null)
        {
            GameGlobal.GetMod<ModCoroutine>().StopCoroutine(_blockBornEffectCoroutine);
            _blockBornEffectCoroutine = null;
        }

        if (_shakeEffectCoroutine != null)
        {
            GameGlobal.GetMod<ModCoroutine>().StopCoroutine(_shakeEffectCoroutine);
            _shakeEffectCoroutine = null;
        }

        if (sTipFly != null)
        {
            sTipFly.Complete();
            sTipFly = null;
        }

        if (fromDistory) return;

        if (tempLastBaseBoardIndex >= 0 && gameResultType == EnumGameResultType.EFST_NoResult)
        {
            gameStorage.LastBaseBoardIndex = tempLastBaseBoardIndex + 1;
            tempLastBaseBoardIndex = -1;
        }

        gameSys.HandleQuiteGame(gameResultType);
        gameSys.HandleGameDispose(gameResultType);
        StopGuide();

        DisposeClearGuide();
    }

    private Tween guideTween;
    private bool isTriggerGuide = false;

    public static void GetGuidePutPos(ref Vector2Int tPos)
    {
        tPos = ModGame.InvaildPos;
        var guide = GameGlobal.GetMod<GuideSys>();
        int step = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep;
        //var guildGroup = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.ABTestGuide);
        if (step > 2 /*|| (!guide.IsFinished("GUIDE_104") && guildGroup == EABTestGroup.Group1)*/)
        {
            return;
        }

        if (step == 0)
        {
            tPos.x = 3;
            tPos.y = 3;
        }
        else if (step == 1)
        {
            tPos.x = 3;
            tPos.y = 2;
        }

    }

    private void PlayGuide()
    {
        var guide = GameGlobal.GetMod<GuideSys>();
        int step = SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep;
        var guildGroup = GameGlobal.GetMod<ModABTest>().GetABTestGroup(EABTestType.ABTestGuide);
        if (step > 2 /*|| (!guide.IsFinished("GUIDE_104") && guildGroup == EABTestGroup.Group1)*/)
        {
            return;
        }

        isTriggerGuide = true;
        var thePos = UINode_Main.parent.InverseTransformPoint(UINode_Content.position);
        GameObject finger;
        if (step < 1)
        {
            finger = UINode_finger2.gameObject;
            BIHelper.SendGameEvent(guildGroup == EABTestGroup.Group2
                ? BiEventBlockMatch1.Types.GameEventType.GameEventFteTestGameClickBlock1
                : BiEventBlockMatch1.Types.GameEventType.GameEventFteGameClickBlock1);

        }
        else
        {
            thePos.x -= Grid.HalfGridWidth;
            finger = UINode_finger1.gameObject;
            BIHelper.SendGameEvent(guildGroup == EABTestGroup.Group2
                ? BiEventBlockMatch1.Types.GameEventType.GameEventFteTestGameClickBlock2
                : BiEventBlockMatch1.Types.GameEventType.GameEventFteGameClickBlock2);
        }


        finger.SetActive(true);
        finger.transform.localPosition = new Vector3(0, UINode_BlockArea.localPosition.y, 0);
        guideTween = finger.transform.DOLocalMove(thePos, 2.3f).SetLoops(999).SetEase(Ease.OutQuad);
        guideTween.PlayForward();
    }

    private void StopGuide()
    {
        if (isTriggerGuide)
        {
            SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep++;
            if (SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessStep >= 2)
            {
                SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.GuideEndLessEnd = true;
            }
        }

        guideTween?.Kill();
        UINode_finger1.gameObject.SetActive(false);
        UINode_finger2.gameObject.SetActive(false);
    }


    #region 清屏引导

    private bool isPlayClearGuide = false;
    private Tween clearFingerTween;
    private List<BlockPutInfo> tempClearList = new List<BlockPutInfo>();
    private List<RectTransform> tempBlockList = new List<RectTransform>();
    private int tempClearIndex = 0;
    private int tempCurIndex = 0;
    private GameObject tempBlock;

    private const int MaxClearGuideSteps = 2;
    private const float GuideMoveDuration = 2.3f;
    private const int GuideLoopCount = 9999;

    protected int[] SelectIndexs = new[] { 0, 1, 2 };
    protected int[] SelectIndexsNew = new[] { 1, 2, 0 };
    /// <summary>
    /// 玩家点击方块时检查是否匹配当前引导步骤
    /// </summary>
    private void OnSelectBlock(EventBlockClick evt)
    {
        if (evt.SelectIndex != tempClearIndex)
        {
            ResetClearGuide();
        }
    }

    /// <summary>
    /// 销毁所有清屏引导相关资源
    /// </summary>
    private void DisposeClearGuide()
    {

        StopClearScreenGuide();
        tempClearList.Clear();
        tempBlockList.Clear();

        if (tempBlock != null)
        {
            GameObject.Destroy(tempBlock);
            tempBlock = null;
        }

        UINode_TempFinger1.gameObject.SetActive(false);
    }

    /// <summary>
    /// 尝试触发清屏引导逻辑
    /// 条件：
    /// - 当前关卡支持清屏类型
    /// - 用户未触发过清屏引导
    /// - 最高分 > 1800
    /// - 清屏块数量在允许范围内（1~2）
    /// </summary>
    public void TryTriggerClearGuide(EnumBlockProductType curProductType, List<BlockPutInfo> clearList,
        List<RectTransform> blockList)
    {
        if (
            curProductType != EnumBlockProductType.EBPT_ClearScreen ||
            SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.ClearScreenGuide ||
            StorageExtension.GameEndlssStorage.CurFirstScore < 1800 ||
            clearList.Count <= 0 || clearList.Count > MaxClearGuideSteps)
        {
            CLog.Info($"无法触发清屏引导--》:curProductType:{curProductType}  " +
                      $"是否已经清屏过:{SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.ClearScreenGuide} " +
                      $"当前最大得分：{ StorageExtension.GameEndlssStorage.CurFirstScore}  clearList.Count：{ clearList.Count}");
            return;
        }

        tempClearList.AddRange(clearList);
        tempBlockList.Clear();
        tempBlockList.AddRange(blockList);
        SDK<IStorage>.Instance.Get<StorageGlobal>().Guide.ClearScreenGuide = true;

        StopClearScreenGuide();
        isPlayClearGuide = true;
        tempCurIndex = 0;
        PlayClearScreenGuide();
    }

    /// <summary>
    /// 停止当前清屏引导动画并清理资源
    /// </summary>
    private void StopClearScreenGuide()
    {
        clearFingerTween?.Kill();
        clearFingerTween = null;

        UINode_TempFinger1.gameObject.SetActive(false);

        if (tempBlock != null)
        {
            GameObject.DestroyImmediate(tempBlock);
            tempBlock = null;
        }
    }

    /// <summary>
    /// 初始化并播放清屏引导动画
    /// </summary>
    private void PlayClearScreenGuide()
    {
        // 设置初始位置
        UINode_TempFinger1.transform.localPosition = Vector3.zero;
        tempClearIndex = gameSys.CurBlockIsNewOrder ? SelectIndexsNew[tempCurIndex]: SelectIndexs[tempCurIndex];
        var info = tempClearList[tempCurIndex];
        Vector3 targetPosition = gameSys.GetBoardUIPosByPutInfo(info);
        CLog.Info($"触发清屏引导--》:index:{tempClearIndex} targetPosition：{targetPosition} info targetPos:{info.targetPos} blockConfig:{info.blockConfig}");
        // targetPosition += UINode_Content.transform.localPosition;

        // 实例化透明方块
        InstantiateTransparentBlock(info);

        // 设置手指位置
        SetFingerPositionBasedOnIndex();

        // 显示引导手指并开始移动动画
        UINode_TempFinger1.gameObject.SetActive(true);
        clearFingerTween = UINode_TempFinger1.DOLocalMove(targetPosition, GuideMoveDuration)
            .SetLoops(GuideLoopCount)
            .SetEase(Ease.OutQuad)
            .SetUpdate(true);
    }

    /// <summary>
    /// 实例化一个半透明方块用于引导提示
    /// </summary>
    private void InstantiateTransparentBlock(BlockPutInfo info)
    {
        if (tempBlock != null) GameObject.DestroyImmediate(tempBlock);

        tempBlock = GameObject.Instantiate(tempBlockList[tempClearIndex].gameObject,Vector3.zero,Quaternion.identity,UINode_TempFinger1.transform);
        tempBlock.transform.localScale = Vector3.one;
        tempBlock.transform.SetAsFirstSibling();
        tempBlock.transform.localPosition = Vector3.zero;
        // 设置半透明效果
        var images = tempBlock.GetComponentsInChildren<Image>(true);
        foreach (var image in images)
        {
            var color = image.color;
            image.color = new Color(color.r, color.g, color.b, 0.5f);
        }
    }

    /// <summary>
    /// 根据当前索引设置手指位置
    /// </summary>
    private void SetFingerPositionBasedOnIndex()
    {
        switch (tempClearIndex)
        {
            case 0:
                UINode_TempFinger1.position = UINode_Block1.position;
                break;
            case 1:
                UINode_TempFinger1.position = UINode_Block2.position;
                break;
            case 2:
                UINode_TempFinger1.position = UINode_Block3.position;
                break;
        }
    }

    /// <summary>
    /// 检查是否进入下一步引导
    /// 若用户操作不匹配则重置引导
    /// </summary>
    private void TryShowNextStep_ForClearSceneGuide()
    {
        if (!isPlayClearGuide) return;

        var handleGuide = tempClearList.Count > 0 && tempClearList.Count <= MaxClearGuideSteps;
        if (handleGuide == false)
        {
            return;
        }

        if (tempCurIndex == tempClearList.Count - 1 )
        {
            //end
            ResetClearGuide();
            return;
        }
        StopClearScreenGuide();
        if (tempClearList.Count - 1 > tempCurIndex)
        {
            tempCurIndex++;
        }

        PlayClearScreenGuide();
    }

    /// <summary>
    /// 重置整个清屏引导流程
    /// </summary>
    private void ResetClearGuide()
    {
        if (!isPlayClearGuide) return;
        tempClearList.Clear();
        StopClearScreenGuide();
        isPlayClearGuide = false;
        tempClearIndex = 0;
        tempCurIndex = 0;
    }

    #endregion


    #region GM相关

    private GameButton hideDebugBtn;

    private void OnShowDebugBtn()
    {
        UINode_Debug.gameObject.SetActive(true);
        if (hideDebugBtn == null)
        {
            hideDebugBtn = UINode_Debug.GetComponent<GameButton>();
            hideDebugBtn.onClick.AddListener(OnHideDebugBtn);
        }

        RefreshComplexlnfo();
    }

    void RefreshComplexlnfo()
    {
#if DEVELOPMENT_BUILD
        var theValue = BlockPlayManager.Instance.Complexity;
        string infoStr = $"Complex:{theValue}\nSparsity:{BlockPlayManager.Instance.Sparsity},Connected:" +
                         $"{BlockPlayManager.Instance.ConnectedComponentsCount}\nEdgeCount:{BlockPlayManager.Instance.EdgeCountEx},RowAndColum:{BlockPlayManager.Instance.RowColVariability}\nEntropy:{BlockPlayManager.Instance.Entropy}";
        UITxt_Complexlnfo.SetText(infoStr);
        UITxt_Complex.SetText(theValue.ToString());
#endif
    }

    private void OnHideDebugBtn()
    {
        UINode_Debug.gameObject.SetActive(false);
    }

    #region Reward Item Event Handlers

    private void OnRewardItemSpawned(EventRewardItemSpawned evt)
    {
        CLog.Info($"UI LOG: 接到奖励物品生成事件，位置: {evt.Item.position}");
        // TODO: 根据evt.Item.position获取对应的方块视图对象 (Block View)
        // TODO: 激活该方块视图下的奖励物品图标 (例如宝箱)
        // TODO: 更新该方块视图下的倒计时文本，显示: {evt.Item.countdown}
    }

    private void OnRewardItemUpdated(EventRewardItemUpdated evt)
    {
        CLog.Info($"UI LOG: 接到奖励物品更新事件，位置: {evt.Item.position}，新倒计时: {evt.Item.countdown}");
        // TODO: 根据evt.Item.position获取对应的方块视图对象
        // TODO: 更新该方块视图下的倒计时文本，显示: {evt.Item.countdown}
    }

    private void OnRewardItemRemoved(EventRewardItemRemoved evt)
    {
        CLog.Info($"UI LOG: 接到奖励物品移除事件，位置: {evt.Position}，是否被收集: {evt.Collected}");
        // TODO: 根据evt.Position获取对应的方块视图对象
        // TODO: 隐藏该方块视图下的奖励物品图标和倒计时文本

        if (evt.Collected)
        {
            // TODO: 在该位置播放奖励收集特效 (例如金币飞向装修币图标的动画)
        }
    }

    #endregion

    private void OnRandomBlockBtn()
    {
        gameSys.Debug_GenerateBlockGo(EnumBlockProductType.EBPT_Normal);
    }

    private void OnHardBtn()
    {
        gameSys.Debug_GenerateBlockGo(EnumBlockProductType.EBPT_Hard);
    }

    private void OnComplexityUpBtn()
    {
        gameSys.Debug_GenerateBlockGo(EnumBlockProductType.EBPT_ComplexityUp);
    }

    private void OnComplexityDownBtn()
    {
        gameSys.Debug_GenerateBlockGo(EnumBlockProductType.EBPT_ComplexityDown);
    }

#if DEBUG || DEVELOPMENT_BUILD
    public void HandleChangeBlockPlan()
    {
        if (gameSys == null) return;
        var thePlan = gameSys.CurBlockProductIsNative ? "Native" : "Local";
        UITxt_BlockPlan.SetText(thePlan);
    }
#endif

    #endregion


}
