using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using DragonCD2.UserSegmentation.Bean;
using Framework;
using RulesEngine.Extensions;
using RulesEngine.Models;
using RulesEngine;
using UnityEngine;
using UnityEngine.Pool;

namespace DragonCD2.UserSegmentation.Rule.Validator
{
    internal class FlexibleRuleValidator
    {
        private static readonly ReSettings ReSettings = new()
        {
            UseFastExpressionCompiler = false,
            CustomTypes = new[] { typeof(RuleValidateHelper) }
        };

        private static readonly List<RuleParameter> RuleExpressionParameters = new();

        public static bool InvokeValidate(ISegmentationEngine segmentationEngine, ChooseStoryRule rule)
        {
            if (string.IsNullOrEmpty(rule.Condition))
            {
                return true;
            }

            const string workFlowName = "Validate user segmentation rule!";

            var workflows = ListPool<Workflow>.Get();
            var workflow = new Workflow
            {
                WorkflowName = workFlowName
            };

            var ruleExpression = AnalyzeExpression(segmentationEngine, rule.Condition);

            CLog.Info($"[无尽剧本] 正在过滤规则表达式:[{ruleExpression}]");
            var rules = new List<RulesEngine.Models.Rule>()
            {
                new()
                {
                    RuleName = "validate rule by expression!",
                    SuccessEvent = "用户的行为数据符合该分层规则!",
                    ErrorMessage = "用户的行为数据不符合该分层规则!",
                    Expression = ruleExpression,
                    RuleExpressionType = RuleExpressionType.LambdaExpression
                }
            };
            workflow.Rules = rules;
            workflows.Add(workflow);

            var engine = new RulesEngine.RulesEngine(workflows.ToArray(), ReSettings);
            var resultList = engine.ExecuteAllRulesAsync(workFlowName, RuleExpressionParameters.ToArray()).Result;

            ListPool<Workflow>.Release(workflows);

            resultList.OnSuccess((@event) => { CLog.Info($"[无尽剧本] 分层[{rule.Condition}]的规则过滤结果：[{@event}]."); });

            resultList.OnFail(() =>
            {
                foreach (var ruleResultTree in resultList)
                {
                    CLog.Warning($"[无尽剧本] 分层[{rule.Condition}]的规则过滤结果：[{ruleResultTree.ExceptionMessage}]");
                }
            });

            return resultList.TrueForAll(r => r.IsSuccess);
        }
        public static bool InvokeValidateSkill(ISegmentationEngine segmentationEngine, ChooseSkillRule rule)
        {
            if (string.IsNullOrEmpty(rule.Condition))
            {
                return true;
            }

            const string workFlowName = "Validate skill segmentation rule!";

            var workflows = ListPool<Workflow>.Get();
            var workflow = new Workflow
            {
                WorkflowName = workFlowName
            };

            var ruleExpression = AnalyzeExpression(segmentationEngine, rule.Condition);

            CLog.Info($"[无尽剧本] 正在过滤规则表达式:[{ruleExpression}]");
            var rules = new List<RulesEngine.Models.Rule>()
            {
                new()
                {
                    RuleName = "validate rule by expression!",
                    SuccessEvent = "用户的行为数据符合该分层规则!",
                    ErrorMessage = "用户的行为数据不符合该分层规则!",
                    Expression = ruleExpression,
                    RuleExpressionType = RuleExpressionType.LambdaExpression
                }
            };
            workflow.Rules = rules;
            workflows.Add(workflow);

            var engine = new RulesEngine.RulesEngine(workflows.ToArray(), ReSettings);
            var resultList = engine.ExecuteAllRulesAsync(workFlowName, RuleExpressionParameters.ToArray()).Result;

            ListPool<Workflow>.Release(workflows);

            resultList.OnSuccess((@event) => { CLog.Info($"[无尽剧本] 分层[{rule.Condition}]的规则过滤结果：[{@event}]."); });

            resultList.OnFail(() =>
            {
                foreach (var ruleResultTree in resultList)
                {
                    CLog.Warning($"[无尽剧本] 分层[{rule.Condition}]的规则过滤结果：[{ruleResultTree.ExceptionMessage}]");
                }
            });

            return resultList.TrueForAll(r => r.IsSuccess);
        }
        private static string AnalyzeExpression(ISegmentationEngine segmentationEngine, string expression)
        {
            const string RULE_EXPRESSION_PATTERN = @"\{([^}]*)\}|《([^》]*)》";

            RuleExpressionParameters.Clear();

            return Regex.Replace(expression, RULE_EXPRESSION_PATTERN, Analyze);

            string Analyze(Match match)
            {
                // 根据匹配到的内容生成动态替换文本
                var placeHolderValue = "";

                for (int i = 1; i < match.Groups.Count; i++)
                {
                    if (!string.IsNullOrEmpty(match.Groups[i].Value))
                    {
                        placeHolderValue = match.Groups[i].Value;
                        break;
                    }
                }

                if (placeHolderValue.Equals("In") || placeHolderValue.Equals("Nin"))
                {
                    return $"{nameof(RuleValidateHelper)}.{placeHolderValue}";
                }

                if (segmentationEngine.RuleParamsDictionary.TryGetValue(placeHolderValue, out var paramInstance))
                {
                    if (!RuleExpressionParameters.Any(parameter => parameter.Name.Equals(placeHolderValue)))
                    {
                        RuleExpressionParameters.Add(new RuleParameter(placeHolderValue, paramInstance.Value));
                    }

                    return placeHolderValue;
                }
                else
                {
                    CLog.Error($"[无尽剧本] : 条件参数{placeHolderValue}占位找不到对应的程序参数对象!");
                }

                return match.Groups[0].Value;
            }
        }

        public static class RuleValidateHelper
        {
            public static bool In(object param, params object[] paramArray)
            {
                return paramArray.Contains(param);
            }

            public static bool Nin(object param, params object[] paramArray)
            {
                return !paramArray.Contains(param);
            }
        }
    }
}